/**
 * @file 统计筛选器组件
 * @description 数字化统计页面的筛选器组件
 * <AUTHOR> Assistant
 * @date 2025-09-09
 */

import { useDropdownFix } from '@/components/DropdownFix';
import { ReloadOutlined } from '@ant-design/icons';
import { But<PERSON>, Card, DatePicker, Select, Space } from 'antd';
import type { Dayjs } from 'dayjs';
import React from 'react';

export interface StatisticsFiltersProps {
  selectedRegion: string;
  dateRange: [Dayjs, Dayjs] | null;
  loading?: boolean;
  onRegionChange: (value: string) => void;
  onDateRangeChange: (dates: [Dayjs, Dayjs] | null) => void;
  onRefresh: () => void;
}

/**
 * 统计筛选器组件
 */
export const StatisticsFilters: React.FC<StatisticsFiltersProps> = ({
  selectedRegion,
  dateRange,
  loading = false,
  onRegionChange,
  onDateRangeChange,
  onRefresh,
}) => {
  // 修复下拉框定位问题
  useDropdownFix();

  return (
    <Card style={{ marginBottom: 24 }}>
      <div
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          flexWrap: 'wrap',
          gap: 16,
        }}
      >
        <Space size="large" wrap>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <span style={{ marginRight: 8, fontWeight: 'bold', minWidth: 80 }}>
              区域筛选：
            </span>
            <Select
              style={{ width: 200 }}
              value={selectedRegion}
              onChange={onRegionChange}
              placeholder="选择区域"
              getPopupContainer={(triggerNode) => {
                // 查找最近的Card容器作为定位参考
                let parent = triggerNode?.parentElement;
                while (parent) {
                  if (
                    parent.classList.contains('ant-card') ||
                    parent.classList.contains('content-card')
                  ) {
                    return parent;
                  }
                  parent = parent.parentElement;
                }
                return document.body;
              }}
              options={[
                { label: '全部区域', value: 'all' },
                { label: '西安区域', value: '1' },
                { label: '咸阳区域', value: '2' },
                { label: '宝鸡区域', value: '3' },
                { label: '渭南区域', value: '4' },
              ]}
            />
          </div>

          <div style={{ display: 'flex', alignItems: 'center' }}>
            <span style={{ marginRight: 8, fontWeight: 'bold', minWidth: 80 }}>
              时间范围：
            </span>
            <DatePicker.RangePicker
              value={dateRange}
              onChange={onDateRangeChange}
              placeholder={['开始时间', '结束时间']}
              style={{ width: 300 }}
              allowClear
              getPopupContainer={(triggerNode) => {
                // 查找最近的Card容器作为定位参考
                let parent = triggerNode?.parentElement;
                while (parent) {
                  if (
                    parent.classList.contains('ant-card') ||
                    parent.classList.contains('content-card')
                  ) {
                    return parent;
                  }
                  parent = parent.parentElement;
                }
                return document.body;
              }}
            />
          </div>
        </Space>

        <Button
          type="primary"
          icon={<ReloadOutlined />}
          onClick={onRefresh}
          loading={loading}
        >
          刷新数据
        </Button>
      </div>
    </Card>
  );
};

export default StatisticsFilters;
