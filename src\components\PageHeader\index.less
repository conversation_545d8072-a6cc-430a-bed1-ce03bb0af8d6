/* 页面头部组件样式 */
.page-header {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  color: white;
  text-align: center;

  /* 山塬页面背景 */
  &.mountain-header-bg {
    background: linear-gradient(
        135deg,
        rgba(139, 69, 19, 70%) 0%,
        rgba(160, 82, 45, 70%) 100%
      ),
      radial-gradient(
        circle at 30% 70%,
        rgba(205, 133, 63, 30%) 0%,
        transparent 50%
      ),
      linear-gradient(
        45deg,
        #8b4513 0%,
        #a0522d 25%,
        #cd853f 50%,
        #8b4513 75%,
        #a0522d 100%
      );
    background-size: cover, 600px 600px, 400% 400%;
    background-position: center, 0 0, 0% 50%;
    animation: gradient-shift 20s ease infinite;
  }

  /* 水系页面背景 */
  &.water-header-bg {
    background: linear-gradient(
        135deg,
        rgba(30, 144, 255, 70%) 0%,
        rgba(0, 191, 255, 70%) 100%
      ),
      radial-gradient(
        circle at 70% 30%,
        rgba(135, 206, 235, 30%) 0%,
        transparent 50%
      ),
      linear-gradient(
        45deg,
        #1e90ff 0%,
        #00bfff 25%,
        #87ceeb 50%,
        #1e90ff 75%,
        #00bfff 100%
      );
    background-size: cover, 600px 600px, 400% 400%;
    background-position: center, 0 0, 0% 50%;
    animation: gradient-shift 18s ease infinite;
  }

  /* 历史要素页面背景 */
  &.history-header-bg {
    background: linear-gradient(
        135deg,
        rgba(184, 134, 11, 70%) 0%,
        rgba(146, 64, 14, 70%) 100%
      ),
      radial-gradient(
        circle at 50% 80%,
        rgba(218, 165, 32, 30%) 0%,
        transparent 50%
      ),
      linear-gradient(
        45deg,
        #b8860b 0%,
        #92400e 25%,
        #daa520 50%,
        #b8860b 75%,
        #92400e 100%
      );
    background-size: cover, 600px 600px, 400% 400%;
    background-position: center, 0 0, 0% 50%;
    animation: gradient-shift 22s ease infinite;
  }

  /* 数字化页面背景 */
  &.digital-header-bg {
    background: linear-gradient(
        135deg,
        rgba(184, 134, 11, 70%) 0%,
        rgba(146, 64, 14, 70%) 100%
      ),
      radial-gradient(
        circle at 50% 80%,
        rgba(218, 165, 32, 30%) 0%,
        transparent 50%
      ),
      linear-gradient(
        45deg,
        #b8860b 0%,
        #92400e 25%,
        #daa520 50%,
        #b8860b 75%,
        #92400e 100%
      );
    background-size: cover, 600px 600px, 400% 400%;
    background-position: center, 0 0, 0% 50%;
    animation: gradient-shift 20s ease infinite;
  }

  /* 装饰性背景元素 */
  .header-decorations {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;

    .decoration-particle {
      position: absolute;
      border-radius: 50%;
      background: rgba(255, 255, 255, 10%);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 20%);

      &.decoration-particle-1 {
        width: 120px;
        height: 120px;
        top: 15%;
        right: 10%;
        animation: float-header-1 12s ease-in-out infinite;
      }

      &.decoration-particle-2 {
        width: 80px;
        height: 80px;
        bottom: 20%;
        left: 15%;
        animation: float-header-2 15s ease-in-out infinite;
      }

      &.decoration-particle-3 {
        width: 60px;
        height: 60px;
        top: 40%;
        right: 25%;
        animation: float-header-3 18s ease-in-out infinite;
        border-radius: 15px;
      }
    }

    .decoration-line {
      position: absolute;
      background: linear-gradient(
        45deg,
        rgba(255, 255, 255, 20%) 0%,
        transparent 100%
      );
      border-radius: 2px;

      &.decoration-line-1 {
        width: 150px;
        height: 2px;
        top: 25%;
        right: 30%;
        transform: rotate(45deg);
        animation: line-glow-header 4s ease-in-out infinite;
      }

      &.decoration-line-2 {
        width: 100px;
        height: 2px;
        bottom: 30%;
        left: 20%;
        transform: rotate(-30deg);
        animation: line-glow-header 5s ease-in-out infinite 1s;
      }
    }
  }

  /* 内容区域 */
  .page-header-content {
    position: relative;
    z-index: 3;
    max-width: 800px;
    padding: 0 24px;
    animation: header-content-fade-in 1.2s ease-out;

    .page-header-title {
      color: white !important;
      font-size: 3.5rem;
      font-weight: 800;
      margin-bottom: 24px;
      text-shadow: 0 4px 8px rgba(0, 0, 0, 30%), 0 2px 4px rgba(0, 0, 0, 20%);
      line-height: 1.1;
      letter-spacing: -0.02em;
      background: linear-gradient(135deg, #fff 0%, #f0f8ff 100%);
      background-clip: text;
      -webkit-text-fill-color: transparent;

      @media (max-width: 768px) {
        font-size: 2.5rem;
      }
    }

    .page-header-description {
      color: rgba(255, 255, 255, 95%) !important;
      font-size: 1.3rem;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 30%);
      line-height: 1.6;
      font-weight: 300;
      margin-bottom: 0;

      @media (max-width: 768px) {
        font-size: 1.1rem;
      }
    }
  }

  /* 遮罩层 */
  .page-header-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        135deg,
        rgba(0, 0, 0, 10%) 0%,
        rgba(0, 0, 0, 30%) 100%
      ),
      radial-gradient(
        ellipse at center,
        transparent 0%,
        rgba(0, 0, 0, 10%) 100%
      );
    z-index: 2;
  }

  @media (max-width: 768px) {
    min-height: 300px !important;

    .header-decorations {
      display: none; /* 在小屏幕上隐藏装饰元素 */
    }

    .page-header-content {
      padding: 0 16px;
    }
  }
}

/* 动画效果 */
@keyframes header-content-fade-in {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float-header-1 {
  0%,
  100% {
    transform: translateY(0) rotate(0deg);
  }

  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

@keyframes float-header-2 {
  0%,
  100% {
    transform: translateX(0) translateY(0);
  }

  50% {
    transform: translateX(15px) translateY(-15px);
  }
}

@keyframes float-header-3 {
  0%,
  100% {
    transform: translateY(0) scale(1);
  }

  50% {
    transform: translateY(-18px) scale(1.1);
  }
}

@keyframes line-glow-header {
  0%,
  100% {
    opacity: 0.3;
    box-shadow: 0 0 10px rgba(255, 255, 255, 30%);
  }

  50% {
    opacity: 0.8;
    box-shadow: 0 0 20px rgba(255, 255, 255, 60%);
  }
}

@keyframes gradient-shift {
  0% {
    background-position: center, 0 0, 0% 50%;
  }

  50% {
    background-position: center, 0 0, 100% 50%;
  }

  100% {
    background-position: center, 0 0, 0% 50%;
  }
}
