# 高德地图配置说明

## 概述

关中历史城镇营建经验数据分析平台已经集成了您提供的高德地图封装组件，用于在首页展示关中地区的地理要素分布。

## 配置步骤

### 1. 申请高德地图 API Key

1. 访问 [高德开放平台](https://lbs.amap.com/)
2. 注册并登录账号
3. 创建应用，选择 "Web 端(JS API)"
4. 获取 API Key 和安全密钥（可选）

### 2. 配置环境变量

在项目根目录创建 `.env.local` 文件（如果不存在），添加以下配置：

```bash
# 高德地图 API Key
AMAP_KEY=your-actual-amap-key

# 高德地图安全密钥（可选，用于更高的安全性）
AMAP_SECURITY_CODE=your-security-code
```

### 3. 修改全局配置

高德地图配置已经在 `config/config.ts` 中定义为全局配置：

```typescript
// config/config.ts
export default defineConfig({
  // ... 其他配置
  define: {
    AMAP_CONFIG: {
      key: process.env.AMAP_KEY || 'your-amap-key',
      securityJsCode: process.env.AMAP_SECURITY_CODE || 'your-security-code',
      // ... 其他配置
    },
  },
});
```

对应的类型声明在 `typings.d.ts` 中：

```typescript
declare const AMAP_CONFIG: {
  key: string;
  securityJsCode: string;
  // ... 其他类型定义
};
```

这样就可以在项目中直接使用 `AMAP_CONFIG` 全局变量了。

## 当前实现

### 首页地图功能

- **地图中心**：关中地区中心坐标 (108.9398, 34.3412)
- **缩放级别**：初始缩放级别 8，点击标记点时缩放到 12
- **标记点类型**：
  - 🏔️ 山塬（蓝色标记）
  - 🌊 水系（红色标记）
  - 🏛️ 历史要素（绿色标记）

### 交互功能

1. **标记点点击**：点击地图上的标记点会弹出详情模态框
2. **信息窗体**：显示要素的基本信息和历史记载
3. **详情跳转**：可以从模态框跳转到详细页面

### 数据源

标记点数据来自 `src/services/mockData.ts` 中的：

- `mountainData` - 山塬数据
- `waterSystemData` - 水系数据
- `historicalElementData` - 历史要素数据

## 组件使用

首页中的地图组件使用方式：

```tsx
<GaoDeMap
  city="西安"
  center={[108.9398, 34.3412]}
  zoom={8}
  activedZoom={12}
  markers={prepareMapMarkers()}
  enableInfoWindow={true}
  enableCluster={false}
  events={{
    onClick: handleMapClick,
    onMarkerClick: handleMarkerClick,
  }}
  onMapCreated={handleMapCreated}
  style={{ width: '100%', height: '100%' }}
/>
```

## 故障排除

### 1. 地图无法加载

- 检查 API Key 是否正确配置
- 检查网络连接是否正常
- 查看浏览器控制台是否有错误信息

### 2. 标记点不显示

- 检查标记点数据格式是否正确
- 检查坐标是否在有效范围内
- 检查图标 URL 是否可访问

### 3. 事件不响应

- 检查事件处理函数是否正确绑定
- 检查控制台是否有 JavaScript 错误

## 扩展功能

您可以根据需要扩展以下功能：

1. **聚合功能**：设置 `enableCluster={true}` 启用标记点聚合
2. **自定义控件**：通过 `customControls` 属性添加自定义控件
3. **地图样式**：修改地图样式和主题
4. **更多交互**：添加拖拽、缩放等事件处理

## 注意事项

1. 高德地图 API 有使用配额限制，请根据实际需求选择合适的套餐
2. 生产环境部署时，建议配置域名白名单以提高安全性
3. 定期检查 API Key 的有效期和使用情况

## 相关文档

- [高德地图 JavaScript API 文档](https://lbs.amap.com/api/javascript-api/summary)
- [GaoDeMap 组件文档](../src/components/GaoDeMap/README.md)
- [关中历史城镇营建经验数据分析平台文档](../README.md)
