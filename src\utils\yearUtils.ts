/**
 * 年份处理工具函数
 * 支持公元前年份的格式化、解析和验证
 */

/**
 * 年份格式化函数
 * @param year 年份数字（负数表示公元前）
 * @returns 格式化后的年份字符串
 */
export const formatYear = (year: number | null | undefined): string => {
  if (!year && year !== 0) return '-';

  if (year < 0) {
    return `公元前${Math.abs(year)}年`;
  } else if (year > 0) {
    return `公元${year}年`;
  } else {
    // year === 0，历史上不存在公元0年
    return '-';
  }
};

/**
 * 年份解析函数（从字符串解析）
 * @param yearStr 年份字符串
 * @returns 解析后的年份数字，失败返回null
 */
export const parseYear = (yearStr: string): number | null => {
  if (!yearStr || typeof yearStr !== 'string') return null;

  // 去除空格
  const trimmed = yearStr.trim();
  if (!trimmed) return null;

  // 处理"公元前XXX年"格式
  const bcMatch = trimmed.match(/公元前(\d+)年?/);
  if (bcMatch) {
    const year = parseInt(bcMatch[1], 10);
    return isNaN(year) ? null : -year;
  }

  // 处理"公元XXX年"格式
  const adMatch = trimmed.match(/公元(\d+)年?/);
  if (adMatch) {
    const year = parseInt(adMatch[1], 10);
    return isNaN(year) ? null : year;
  }

  // 处理纯数字
  const num = parseInt(trimmed, 10);
  return isNaN(num) ? null : num;
};

/**
 * 年份验证函数
 * @param year 年份数字
 * @returns 验证结果对象
 */
export const validateYear = (
  year: number | null | undefined,
): {
  valid: boolean;
  message?: string;
} => {
  if (year === null || year === undefined) {
    return { valid: true }; // 允许为空
  }

  if (year === 0) {
    return {
      valid: false,
      message: '年份不能为0，请使用公元前1年（-1）或公元1年（1）',
    };
  }

  const currentYear = new Date().getFullYear();
  if (year > currentYear) {
    return {
      valid: false,
      message: '建造年份不能晚于当前年份',
    };
  }

  if (year < -5000) {
    return {
      valid: false,
      message: '建造年份不能早于公元前5000年',
    };
  }

  return { valid: true };
};

/**
 * 年份范围验证函数
 * @param startYear 起始年份
 * @param endYear 结束年份
 * @returns 验证结果对象
 */
export const validateYearRange = (
  startYear: number | null | undefined,
  endYear: number | null | undefined,
): { valid: boolean; message?: string } => {
  // 单独验证每个年份
  const startValidation = validateYear(startYear);
  if (!startValidation.valid) {
    return {
      valid: false,
      message: `起始年份错误：${startValidation.message}`,
    };
  }

  const endValidation = validateYear(endYear);
  if (!endValidation.valid) {
    return { valid: false, message: `结束年份错误：${endValidation.message}` };
  }

  // 验证范围逻辑
  if (
    startYear !== null &&
    startYear !== undefined &&
    endYear !== null &&
    endYear !== undefined
  ) {
    if (startYear > endYear) {
      return {
        valid: false,
        message: '起始年份不能晚于结束年份',
      };
    }
  }

  return { valid: true };
};

/**
 * 获取年份输入提示文本
 * @returns 提示文本
 */
export const getYearInputHint = (): string => {
  return '格式说明：正数表示公元年份，负数表示公元前年份。例如：652 = 公元652年，-221 = 公元前221年';
};

/**
 * 获取年份输入示例
 * @returns 示例数组
 */
export const getYearInputExamples = (): Array<{
  year: number;
  description: string;
}> => {
  return [
    { year: 2023, description: '公元2023年' },
    { year: 652, description: '公元652年' },
    { year: 1, description: '公元1年' },
    { year: -1, description: '公元前1年' },
    { year: -221, description: '公元前221年' },
    { year: -2000, description: '公元前2000年' },
  ];
};

/**
 * 年份排序函数（升序）
 * @param a 第一个年份
 * @param b 第二个年份
 * @returns 排序结果
 */
export const sortYearAsc = (
  a: number | null | undefined,
  b: number | null | undefined,
): number => {
  // 处理null/undefined值，将其排在最后
  if ((a === null || a === undefined) && (b === null || b === undefined))
    return 0;
  if (a === null || a === undefined) return 1;
  if (b === null || b === undefined) return -1;

  return a - b;
};

/**
 * 年份排序函数（降序）
 * @param a 第一个年份
 * @param b 第二个年份
 * @returns 排序结果
 */
export const sortYearDesc = (
  a: number | null | undefined,
  b: number | null | undefined,
): number => {
  return -sortYearAsc(a, b);
};

/**
 * 判断年份是否在指定范围内
 * @param year 要检查的年份
 * @param startYear 起始年份（可选）
 * @param endYear 结束年份（可选）
 * @returns 是否在范围内
 */
export const isYearInRange = (
  year: number | null | undefined,
  startYear?: number | null,
  endYear?: number | null,
): boolean => {
  if (year === null || year === undefined) return false;

  if (startYear !== null && startYear !== undefined && year < startYear) {
    return false;
  }

  if (endYear !== null && endYear !== undefined && year > endYear) {
    return false;
  }

  return true;
};

/**
 * 获取当前年份
 * @returns 当前年份
 */
export const getCurrentYear = (): number => {
  return new Date().getFullYear();
};

/**
 * 年份输入框的配置选项
 */
export const YEAR_INPUT_CONFIG = {
  min: -5000,
  max: getCurrentYear(),
  placeholder: '请输入年份（负数表示公元前）',
  step: 1,
  precision: 0,
} as const;
