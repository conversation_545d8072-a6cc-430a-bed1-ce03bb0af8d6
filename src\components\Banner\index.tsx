/**
 * @file Banner组件
 * @description 页面顶部横幅组件，支持背景图片和标题文字
 * <AUTHOR> Assistant
 * @date 2025-09-11
 */

import { Typography } from 'antd';
import React from 'react';

const { Title } = Typography;

export interface BannerProps {
  /** 背景图片URL */
  backgroundImage: string;
  /** 标题文字 */
  title: string;
  /** Banner高度，默认200px */
  height?: number | string;
  /** 标题级别，默认为1 */
  titleLevel?: 1 | 2 | 3 | 4 | 5;
  /** 标题字体大小 */
  titleFontSize?: string;
  /** 遮罩层透明度，默认0.4 */
  overlayOpacity?: number;
  /** 自定义样式 */
  style?: React.CSSProperties;
  /** 标题自定义样式 */
  titleStyle?: React.CSSProperties;
}

/**
 * Banner组件
 */
export const Banner: React.FC<BannerProps> = ({
  backgroundImage,
  title,
  height = '200px',
  titleLevel = 1,
  titleFontSize = '2.5rem',
  overlayOpacity = 0.4,
  style,
  titleStyle,
}) => {
  return (
    <div
      style={{
        width: '100%',
        height,
        backgroundImage: `url(${backgroundImage})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat',
        position: 'relative',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        marginBottom: '24px',
        ...style,
      }}
    >
      {/* 遮罩层 */}
      <div
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: `rgba(0, 0, 0, ${overlayOpacity})`,
          zIndex: 1,
        }}
      />

      {/* 标题文字 */}
      <Title
        level={titleLevel}
        style={{
          color: 'white',
          textAlign: 'center',
          margin: 0,
          zIndex: 2,
          position: 'relative',
          textShadow: '2px 2px 4px rgba(0, 0, 0, 0.8)',
          fontSize: titleFontSize,
          fontWeight: 'bold',
          ...titleStyle,
        }}
      >
        {title}
      </Title>
    </div>
  );
};

export default Banner;
