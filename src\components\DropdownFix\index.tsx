/**
 * @file 下拉框定位修复组件
 * @description 修复Ant Design下拉框在页面初始化时定位错误的问题
 * <AUTHOR> Assistant
 * @date 2025-09-11
 */

import { useEffect } from 'react';

/**
 * 下拉框定位修复Hook
 * 用于修复页面初始化时下拉框定位错误的问题
 */
export const useDropdownFix = () => {
  useEffect(() => {
    // 页面加载完成后的修复逻辑
    const fixDropdownPosition = () => {
      // 1. 触发窗口resize事件，让Ant Design重新计算下拉框位置
      window.dispatchEvent(new Event('resize'));

      // 2. 触发scroll事件，确保定位计算正确
      window.dispatchEvent(new Event('scroll'));

      // 3. 检查并修复异常定位的下拉框
      const dropdowns = document.querySelectorAll(
        '.ant-select-dropdown, .ant-picker-dropdown, .ant-cascader-dropdown, .ant-tree-select-dropdown',
      );

      dropdowns.forEach((dropdown) => {
        const element = dropdown as HTMLElement;
        if (
          element.style.visibility === 'hidden' ||
          element.style.display === 'none'
        ) {
          return;
        }

        // 检查是否有异常的定位值
        const style = window.getComputedStyle(element);
        const left = parseFloat(style.left);
        const top = parseFloat(style.top);

        // 如果定位值异常（负值很大或超出屏幕），强制重新定位
        if (
          left < -1000 ||
          left > window.innerWidth ||
          top < -1000 ||
          top > window.innerHeight
        ) {
          console.warn('检测到异常的下拉框定位，正在修复...', { left, top });

          // 重置定位相关样式
          element.style.inset = 'auto';
          element.style.left = 'auto';
          element.style.top = 'auto';
          element.style.right = 'auto';
          element.style.bottom = 'auto';

          // 强制重新计算位置
          void element.offsetHeight; // 触发重排

          // 再次触发resize让Ant Design重新计算
          setTimeout(() => {
            window.dispatchEvent(new Event('resize'));
          }, 10);
        }
      });
    };

    // 延迟执行修复，确保DOM完全渲染
    const timer1 = setTimeout(fixDropdownPosition, 100);
    const timer2 = setTimeout(fixDropdownPosition, 300);
    const timer3 = setTimeout(fixDropdownPosition, 500);

    // 监听页面可见性变化
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        setTimeout(fixDropdownPosition, 100);
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    // 监听窗口焦点变化
    const handleFocus = () => {
      setTimeout(fixDropdownPosition, 100);
    };

    window.addEventListener('focus', handleFocus);

    // 监听DOM变化，实时修复新出现的下拉框
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            const element = node as Element;
            if (
              element.classList.contains('ant-select-dropdown') ||
              element.classList.contains('ant-picker-dropdown') ||
              element.classList.contains('ant-cascader-dropdown') ||
              element.classList.contains('ant-tree-select-dropdown')
            ) {
              // 新的下拉框出现，延迟修复其定位
              setTimeout(fixDropdownPosition, 50);
            }
          }
        });
      });
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true,
    });

    return () => {
      clearTimeout(timer1);
      clearTimeout(timer2);
      clearTimeout(timer3);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('focus', handleFocus);
      observer.disconnect();
    };
  }, []);
};

/**
 * 下拉框定位修复组件
 * 在页面中使用此组件来自动修复下拉框定位问题
 */
export const DropdownFix: React.FC = () => {
  useDropdownFix();
  return null;
};

export default DropdownFix;
