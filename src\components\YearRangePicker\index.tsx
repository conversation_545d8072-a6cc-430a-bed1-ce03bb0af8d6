import {
  getYearInputHint,
  validateYearRange,
  YEAR_INPUT_CONFIG,
} from '@/utils/yearUtils';
import { InfoCircleOutlined } from '@ant-design/icons';
import { InputNumber, Space, Tooltip } from 'antd';
import React from 'react';

export interface YearRangePickerProps {
  value?: [number | null, number | null];
  onChange?: (value: [number | null, number | null]) => void;
  placeholder?: [string, string];
  disabled?: boolean;
  style?: React.CSSProperties;
  className?: string;
}

export const YearRangePicker: React.FC<YearRangePickerProps> = ({
  value = [null, null],
  onChange,
  placeholder = ['起始年份', '结束年份'],
  disabled = false,
  style,
  className,
}) => {
  const [startYear, endYear] = value;

  const handleStartYearChange = (newStartYear: number | null) => {
    const newValue: [number | null, number | null] = [newStartYear, endYear];
    onChange?.(newValue);
  };

  const handleEndYearChange = (newEndYear: number | null) => {
    const newValue: [number | null, number | null] = [startYear, newEndYear];
    onChange?.(newValue);
  };

  // 验证年份范围
  const validation = validateYearRange(startYear, endYear);

  return (
    <div style={style} className={className}>
      <Space.Compact style={{ width: '100%' }}>
        <InputNumber
          style={{ width: '50%' }}
          placeholder={placeholder[0]}
          value={startYear}
          onChange={handleStartYearChange}
          min={YEAR_INPUT_CONFIG.min}
          max={endYear || YEAR_INPUT_CONFIG.max}
          step={YEAR_INPUT_CONFIG.step}
          precision={YEAR_INPUT_CONFIG.precision}
          disabled={disabled}
          status={!validation.valid ? 'error' : undefined}
        />
        <InputNumber
          style={{ width: '50%' }}
          placeholder={placeholder[1]}
          value={endYear}
          onChange={handleEndYearChange}
          min={startYear || YEAR_INPUT_CONFIG.min}
          max={YEAR_INPUT_CONFIG.max}
          step={YEAR_INPUT_CONFIG.step}
          precision={YEAR_INPUT_CONFIG.precision}
          disabled={disabled}
          status={!validation.valid ? 'error' : undefined}
        />
      </Space.Compact>

      {/* 提示信息 */}
      <div
        style={{
          marginTop: 4,
          fontSize: '12px',
          color: validation.valid ? '#666' : '#ff4d4f',
          display: 'flex',
          alignItems: 'center',
          gap: 4,
        }}
      >
        <Tooltip title={getYearInputHint()}>
          <InfoCircleOutlined style={{ color: '#1890ff' }} />
        </Tooltip>
        <span>
          {validation.valid
            ? '负数表示公元前年份，例如：-221 = 公元前221年'
            : validation.message}
        </span>
      </div>
    </div>
  );
};

export default YearRangePicker;
